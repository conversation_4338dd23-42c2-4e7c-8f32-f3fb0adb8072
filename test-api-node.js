/**
 * XoxoMe API 测试脚本 - Node.js版本
 */

// 检查并导入fetch（Node.js 18+有内置fetch，否则需要安装node-fetch）
let fetch;
try {
    fetch = globalThis.fetch;
    if (!fetch) {
        // 尝试导入node-fetch
        fetch = require('node-fetch');
    }
} catch (e) {
    console.error('❌ 需要fetch支持。请升级到Node.js 18+或安装node-fetch: npm install node-fetch');
    process.exit(1);
}

class XoxoMeAPITest {
    constructor() {
        this.config = {
            apiBase: 'https://mail.xoxome.online',
            loginUrl: 'https://mail.xoxome.online/api/auth/login',
            username: 'halo',
            password: '12345678aka'
        };
        this.token = null;
        this.availableSuffixes = null;
    }

    async makeRequest(url, options = {}) {
        try {
            const response = await fetch(url, options);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            throw new Error(`请求失败: ${error.message}`);
        }
    }

    async login() {
        console.log('🔐 开始登录...');
        const result = await this.makeRequest(this.config.loginUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                username: this.config.username,
                password: this.config.password
            })
        });

        if (result.success) {
            this.token = result.token;
            console.log('✅ 登录成功');
            return result;
        } else {
            throw new Error(result.message || '登录失败');
        }
    }

    getHeaders() {
        return {
            'Accept': 'application/json',
            'Authorization': `Bearer ${this.token}`,
            'Cookie': `token=${this.token}`,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        };
    }

    async getSuffixes() {
        console.log('📋 获取域名后缀...');
        const data = await this.makeRequest(`${this.config.apiBase}/api/email/suffixes`, {
            headers: this.getHeaders()
        });
        
        this.availableSuffixes = data.success ? data.data : [];
        console.log('✅ 可用后缀:', this.availableSuffixes);
        return this.availableSuffixes;
    }

    async createEmail() {
        console.log('📧 创建邮箱...');
        if (!this.availableSuffixes?.length) {
            await this.getSuffixes();
        }
        
        const suffix = this.availableSuffixes[Math.floor(Math.random() * this.availableSuffixes.length)];
        console.log('🎯 使用后缀:', suffix);

        const data = await this.makeRequest(`${this.config.apiBase}/api/email/generate`, {
            method: 'POST',
            headers: {
                ...this.getHeaders(),
                'Content-Type': 'application/json',
                'Referer': 'https://mail.xoxome.online/dashboard',
                'Origin': 'https://mail.xoxome.online'
            },
            body: JSON.stringify({ suffix })
        });

        if (data.success) {
            const emailInfo = {
                email: data.data.email,
                sessionId: `sync_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
            };
            console.log('✅ 邮箱创建成功:', emailInfo.email);
            return emailInfo;
        } else {
            throw new Error(data.message || '创建邮箱失败');
        }
    }

    async getEmails(sessionId) {
        const data = await this.makeRequest(
            `${this.config.apiBase}/api/emails/imap-fetch?sessionId=${sessionId}`,
            { headers: { ...this.getHeaders(), 'Referer': 'https://mail.xoxome.online/dashboard' } }
        );
        return data.success ? data.data : [];
    }

    extractCode(content) {
        const patterns = [/验证码[：:\s]*(\d{4,8})/i, /code[：:\s]*(\d{4,8})/i, /(\d{6})/, /(\d{4})/];
        for (const pattern of patterns) {
            const match = content.match(pattern);
            if (match?.[1]) return match[1];
        }
        return null;
    }

    async waitForCode(sessionId, maxRetries = 6) {
        console.log('⏳ 等待验证码...');
        for (let i = 0; i < maxRetries; i++) {
            console.log(`🔄 第 ${i + 1}/${maxRetries} 次尝试`);
            
            const emails = await this.getEmails(sessionId);
            if (emails?.length > 0) {
                console.log(`📬 获取到 ${emails.length} 封邮件`);
                const content = emails[0].text || emails[0].html || '';
                const code = this.extractCode(content);
                if (code) {
                    console.log('✅ 获取到验证码:', code);
                    return code;
                } else {
                    console.log('📧 邮件内容预览:', content.substring(0, 100) + '...');
                }
            } else {
                console.log('📭 暂无邮件');
            }
            
            if (i < maxRetries - 1) {
                console.log('⏰ 5秒后重试...');
                await new Promise(r => setTimeout(r, 5000));
            }
        }
        throw new Error('未获取到验证码');
    }
}

// 测试函数
async function testAPI() {
    console.log('🚀 开始测试 XoxoMe API (Node.js版本)');
    console.log('='.repeat(50));
    
    const api = new XoxoMeAPITest();
    
    try {
        // 登录
        await api.login();
        
        // 创建邮箱
        const emailInfo = await api.createEmail();
        
        console.log('\n📧 测试邮箱:', emailInfo.email);
        console.log('🔑 会话ID:', emailInfo.sessionId);
        console.log('\n💡 现在可以向这个邮箱发送测试邮件');
        console.log('💡 然后运行以下命令获取验证码:');
        console.log(`   testAPI.waitForCode("${emailInfo.sessionId}")`);
        
        // 保存到全局变量
        global.testAPI = api;
        global.testEmailInfo = emailInfo;
        
        console.log('\n✅ 基础测试完成！');
        console.log('='.repeat(50));
        
        return { api, emailInfo };
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.log('='.repeat(50));
        throw error;
    }
}

// 如果直接运行此文件
if (require.main === module) {
    testAPI().catch(console.error);
}

// 导出供其他模块使用
module.exports = { XoxoMeAPITest, testAPI };
