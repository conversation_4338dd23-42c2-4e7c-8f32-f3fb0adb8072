/**
 * XoxoMe API 测试脚本 - Node.js版本
 */

// 检查并导入fetch（Node.js 18+有内置fetch，否则需要安装node-fetch）
let fetch;
try {
    fetch = globalThis.fetch;
    if (!fetch) {
        // 尝试导入node-fetch
        fetch = require('node-fetch');
    }
} catch (e) {
    console.error('❌ 需要fetch支持。请升级到Node.js 18+或安装node-fetch: npm install node-fetch');
    process.exit(1);
}

class XoxoMeAPITest {
    constructor() {
        this.config = {
            apiBase: 'https://mail.xoxome.online',
            loginUrl: 'https://mail.xoxome.online/api/auth/login',
            username: 'halo',
            password: '12345678aka'
        };
        this.token = null;
        this.availableSuffixes = null;
    }

    async makeRequest(url, options = {}) {
        try {
            const response = await fetch(url, options);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            throw new Error(`请求失败: ${error.message}`);
        }
    }

    async login() {
        console.log('🔐 开始登录...');
        const result = await this.makeRequest(this.config.loginUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                username: this.config.username,
                password: this.config.password
            })
        });

        if (result.success) {
            this.token = result.token;
            console.log('✅ 登录成功');
            return result;
        } else {
            throw new Error(result.message || '登录失败');
        }
    }

    getHeaders() {
        return {
            'Accept': 'application/json',
            'Authorization': `Bearer ${this.token}`,
            'Cookie': `token=${this.token}`,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        };
    }

    async getSuffixes() {
        console.log('📋 获取域名后缀...');
        const data = await this.makeRequest(`${this.config.apiBase}/api/email/suffixes`, {
            headers: this.getHeaders()
        });
        
        this.availableSuffixes = data.success ? data.data : [];
        console.log('✅ 可用后缀:', this.availableSuffixes);
        return this.availableSuffixes;
    }

    async createEmail() {
        console.log('📧 创建邮箱...');
        if (!this.availableSuffixes?.length) {
            await this.getSuffixes();
        }
        
        const suffix = this.availableSuffixes[Math.floor(Math.random() * this.availableSuffixes.length)];
        console.log('🎯 使用后缀:', suffix);

        const data = await this.makeRequest(`${this.config.apiBase}/api/email/generate`, {
            method: 'POST',
            headers: {
                ...this.getHeaders(),
                'Content-Type': 'application/json',
                'Referer': 'https://mail.xoxome.online/dashboard',
                'Origin': 'https://mail.xoxome.online'
            },
            body: JSON.stringify({ suffix })
        });

        if (data.success) {
            const emailInfo = {
                email: data.data.email,
                sessionId: `sync_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
            };
            console.log('✅ 邮箱创建成功:', emailInfo.email);
            return emailInfo;
        } else {
            throw new Error(data.message || '创建邮箱失败');
        }
    }

    async getEmails(sessionId) {
        const data = await this.makeRequest(
            `${this.config.apiBase}/api/emails/imap-fetch?sessionId=${sessionId}`,
            { headers: { ...this.getHeaders(), 'Referer': 'https://mail.xoxome.online/dashboard' } }
        );
        return data.success ? data.data : [];
    }

    extractCode(content) {
        const patterns = [/验证码[：:\s]*(\d{4,8})/i, /code[：:\s]*(\d{4,8})/i, /(\d{6})/, /(\d{4})/];
        for (const pattern of patterns) {
            const match = content.match(pattern);
            if (match?.[1]) return match[1];
        }
        return null;
    }

    async waitForCode(sessionId, maxRetries = 6) {
        console.log('⏳ 等待验证码...');
        for (let i = 0; i < maxRetries; i++) {
            console.log(`🔄 第 ${i + 1}/${maxRetries} 次尝试`);
            
            const emails = await this.getEmails(sessionId);
            if (emails?.length > 0) {
                console.log(`📬 获取到 ${emails.length} 封邮件`);
                const content = emails[0].text || emails[0].html || '';
                const code = this.extractCode(content);
                if (code) {
                    console.log('✅ 获取到验证码:', code);
                    return code;
                } else {
                    console.log('📧 邮件内容预览:', content.substring(0, 100) + '...');
                }
            } else {
                console.log('📭 暂无邮件');
            }
            
            if (i < maxRetries - 1) {
                console.log('⏰ 5秒后重试...');
                await new Promise(r => setTimeout(r, 5000));
            }
        }
        throw new Error('未获取到验证码');
    }
}

// 测试函数
async function testAPI() {
    console.log('🚀 开始测试 XoxoMe API (Node.js版本)');
    console.log('='.repeat(50));
    
    const api = new XoxoMeAPITest();
    
    try {
        // 登录
        await api.login();
        
        // 创建邮箱
        const emailInfo = await api.createEmail();
        
        console.log('\n📧 测试邮箱:', emailInfo.email);
        console.log('🔑 会话ID:', emailInfo.sessionId);
        console.log('\n💡 现在可以向这个邮箱发送测试邮件');
        console.log('💡 然后运行以下命令获取验证码:');
        console.log(`   testAPI.waitForCode("${emailInfo.sessionId}")`);
        
        // 保存到全局变量
        global.testAPI = api;
        global.testEmailInfo = emailInfo;
        
        console.log('\n✅ 基础测试完成！');
        console.log('='.repeat(50));
        
        return { api, emailInfo };
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.log('='.repeat(50));
        throw error;
    }
}

// 完整测试函数（包含验证码获取）
async function testAPIComplete() {
    console.log('🚀 开始完整测试 XoxoMe API');
    console.log('='.repeat(50));

    const api = new XoxoMeAPITest();

    try {
        // 1. 登录
        console.log('\n1️⃣ 测试登录');
        await api.login();

        // 2. 创建邮箱
        console.log('\n2️⃣ 测试创建邮箱');
        const emailInfo = await api.createEmail();

        console.log('\n📧 测试邮箱:', emailInfo.email);
        console.log('🔑 会话ID:', emailInfo.sessionId);

        // 3. 等待并获取验证码
        console.log('\n3️⃣ 开始等待验证码（30秒）');
        console.log('💡 请向邮箱发送包含验证码的测试邮件');
        console.log('💡 邮件内容示例: "您的验证码是：123456"');

        try {
            const code = await api.waitForCode(emailInfo.sessionId, 6);
            console.log('\n🎉 验证码获取成功:', code);
        } catch (error) {
            console.log('\n⚠️ 验证码获取超时:', error.message);
            console.log('💡 这是正常的，因为没有真实邮件发送');
        }

        // 保存到全局变量
        global.testAPI = api;
        global.testEmailInfo = emailInfo;

        console.log('\n✅ 完整测试完成！');
        console.log('='.repeat(50));

        return { api, emailInfo };

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.log('='.repeat(50));
        throw error;
    }
}

// 添加持续监控功能到Node.js版本
async function startMonitoringTest() {
    console.log('🚀 开始邮箱监控测试');
    console.log('='.repeat(50));

    const api = new XoxoMeAPITest();

    try {
        // 登录
        await api.login();

        // 创建邮箱
        const emailInfo = await api.createEmail();

        console.log('\n📧 监控邮箱:', emailInfo.email);
        console.log('🔑 会话ID:', emailInfo.sessionId);
        console.log('\n💡 请向这个邮箱发送包含验证码的测试邮件');
        console.log('💡 系统将持续监控并显示所有邮件内容');
        console.log('💡 找到验证码后会自动停止');
        console.log('💡 按 Ctrl+C 可手动停止');

        // 保存到全局变量
        global.monitorAPI = api;
        global.monitorEmailInfo = emailInfo;

        // 开始监控
        const code = await api.waitForVerificationCode(emailInfo.sessionId, 999, 3000); // 999次重试，3秒间隔
        console.log('\n🎉 最终获取到验证码:', code);

        return { api, emailInfo, code };

    } catch (error) {
        console.error('❌ 监控测试失败:', error.message);
        throw error;
    }
}

// 如果直接运行此文件
if (require.main === module) {
    // 检查命令行参数
    const args = process.argv.slice(2);

    console.log('🚀 XoxoMe API 测试选项:');
    console.log('1. --basic 或 -b: 基础测试');
    console.log('2. --complete 或 -c: 完整测试');
    console.log('3. --monitor 或 -m: 持续监控（默认）');
    console.log('');

    if (args.includes('--basic') || args.includes('-b')) {
        console.log('🔧 运行基础测试...');
        testAPI().catch(console.error);
    } else if (args.includes('--complete') || args.includes('-c')) {
        console.log('🔄 运行完整测试...');
        testAPIComplete().catch(console.error);
    } else {
        console.log('🔥 运行持续监控测试（默认）...');
        startMonitoringTest().catch(console.error);
    }
}

// 导出供其他模块使用
module.exports = { XoxoMeAPITest, testAPI, testAPIComplete, startMonitoringTest };
