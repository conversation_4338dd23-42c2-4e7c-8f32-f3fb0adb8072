// ==UserScript==
// @name         AugmentCode注册填写助手
// @namespace    http://tampermonkey.net/
// @version      0.2.0
// @description  自动填写AugmentCode注册信息（邮箱和验证码），不自动点击按钮
// <AUTHOR> name
// @match        https://*.augmentcode.com/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=augmentcode.com
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_log
// @connect      mail.xoxome.online
// ==/UserScript==

(function() {
    'use strict';

    /**
     * XoxoMe 邮箱服务 API 封装类
     * 整合登录认证和邮箱服务功能
     */
    class XoxoMeAPI {
        constructor(config = {}) {
            this.config = {
                apiBase: 'https://mail.xoxome.online',
                loginUrl: 'https://mail.xoxome.online/api/auth/login',
                username: config.username || 'halo',
                password: config.password || '12345678aka',
                ...config
            };

            this.token = null;
            this.user = null;
            this.availableSuffixes = null; // 缓存可用的域名后缀
        }

        /**
         * 登录获取 token
         */
        async login() {
            try {
                // 使用GM_xmlhttpRequest来避免CORS问题
                const result = await new Promise((resolve, reject) => {
                    if (typeof GM_xmlhttpRequest !== 'undefined') {
                        // 在Tampermonkey环境中使用GM_xmlhttpRequest
                        GM_xmlhttpRequest({
                            method: 'POST',
                            url: this.config.loginUrl,
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            data: JSON.stringify({
                                username: this.config.username,
                                password: this.config.password
                            }),
                            onload: function(response) {
                                try {
                                    const result = JSON.parse(response.responseText);
                                    resolve(result);
                                } catch (e) {
                                    reject(new Error('解析响应失败: ' + e.message));
                                }
                            },
                            onerror: function(error) {
                                reject(new Error('请求失败: ' + error.statusText));
                            }
                        });
                    } else {
                        // 在普通浏览器环境中使用fetch（可能遇到CORS问题）
                        fetch(this.config.loginUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                username: this.config.username,
                                password: this.config.password
                            })
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(resolve)
                        .catch(reject);
                    }
                });

                if (result.success) {
                    this.token = result.token;
                    this.user = result.user;
                    logger.log('XoxoMe 登录成功', 'success');
                    return result;
                } else {
                    throw new Error(result.message || '登录失败');
                }
            } catch (error) {
                logger.log('XoxoMe 登录失败: ' + error, 'error');
                throw error;
            }
        }

        /**
         * 确保已登录，如果未登录则自动登录
         */
        async ensureLoggedIn() {
            if (!this.token) {
                await this.login();
            }
        }

        /**
         * 获取请求头
         */
        getHeaders() {
            const headers = {
                'Accept': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            };

            if (this.token) {
                headers['Authorization'] = `Bearer ${this.token}`;
                headers['Cookie'] = `token=${this.token}`;
            }

            return headers;
        }

        /**
         * 获取可用的邮箱域名后缀
         */
        async getSuffixes() {
            await this.ensureLoggedIn();

            try {
                const data = await new Promise((resolve, reject) => {
                    if (typeof GM_xmlhttpRequest !== 'undefined') {
                        GM_xmlhttpRequest({
                            method: 'GET',
                            url: `${this.config.apiBase}/api/email/suffixes`,
                            headers: this.getHeaders(),
                            onload: function(response) {
                                try {
                                    const result = JSON.parse(response.responseText);
                                    resolve(result);
                                } catch (e) {
                                    reject(new Error('解析响应失败: ' + e.message));
                                }
                            },
                            onerror: function(error) {
                                reject(new Error('请求失败: ' + error.statusText));
                            }
                        });
                    } else {
                        fetch(`${this.config.apiBase}/api/email/suffixes`, {
                            headers: this.getHeaders()
                        })
                        .then(response => response.json())
                        .then(resolve)
                        .catch(reject);
                    }
                });

                const suffixes = data.success ? data.data : [];
                this.availableSuffixes = suffixes; // 缓存后缀列表
                return suffixes;
            } catch (error) {
                logger.log('获取域名后缀失败: ' + error, 'error');
                throw error;
            }
        }

        /**
         * 随机选择一个可用的域名后缀
         */
        async getRandomSuffix() {
            // 如果没有缓存的后缀列表，先获取
            if (!this.availableSuffixes || this.availableSuffixes.length === 0) {
                await this.getSuffixes();
            }

            if (!this.availableSuffixes || this.availableSuffixes.length === 0) {
                throw new Error('没有可用的域名后缀');
            }

            // 随机选择一个后缀
            const randomIndex = Math.floor(Math.random() * this.availableSuffixes.length);
            return this.availableSuffixes[randomIndex];
        }

        /**
         * 创建临时邮箱
         */
        async createEmail(suffix = null) {
            await this.ensureLoggedIn();

            // 如果没有指定后缀，则随机选择一个
            const emailSuffix = suffix || await this.getRandomSuffix();
            logger.log('使用域名后缀: ' + emailSuffix);

            try {
                const data = await new Promise((resolve, reject) => {
                    const headers = {
                        ...this.getHeaders(),
                        'Content-Type': 'application/json',
                        'Referer': 'https://mail.xoxome.online/dashboard',
                        'Origin': 'https://mail.xoxome.online'
                    };

                    if (typeof GM_xmlhttpRequest !== 'undefined') {
                        GM_xmlhttpRequest({
                            method: 'POST',
                            url: `${this.config.apiBase}/api/email/generate`,
                            headers: headers,
                            data: JSON.stringify({
                                suffix: emailSuffix
                            }),
                            onload: function(response) {
                                try {
                                    const result = JSON.parse(response.responseText);
                                    resolve(result);
                                } catch (e) {
                                    reject(new Error('解析响应失败: ' + e.message));
                                }
                            },
                            onerror: function(error) {
                                reject(new Error('请求失败: ' + error.statusText));
                            }
                        });
                    } else {
                        fetch(`${this.config.apiBase}/api/email/generate`, {
                            method: 'POST',
                            headers: headers,
                            body: JSON.stringify({
                                suffix: emailSuffix
                            })
                        })
                        .then(response => response.json())
                        .then(resolve)
                        .catch(reject);
                    }
                });

                if (data.success) {
                    // 生成 sessionId 用于后续获取邮件
                    const sessionId = `sync_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

                    return {
                        email: data.data.email,
                        prefix: data.data.prefix,
                        suffix: data.data.suffix,
                        sessionId: sessionId
                    };
                } else {
                    throw new Error(data.message || '创建邮箱失败');
                }
            } catch (error) {
                logger.log('创建邮箱失败: ' + error, 'error');
                throw error;
            }
        }

        /**
         * 获取邮件列表
         */
        async getEmails(sessionId) {
            await this.ensureLoggedIn();

            try {
                const data = await new Promise((resolve, reject) => {
                    const headers = {
                        ...this.getHeaders(),
                        'Referer': 'https://mail.xoxome.online/dashboard'
                    };

                    if (typeof GM_xmlhttpRequest !== 'undefined') {
                        GM_xmlhttpRequest({
                            method: 'GET',
                            url: `${this.config.apiBase}/api/emails/imap-fetch?sessionId=${sessionId}`,
                            headers: headers,
                            onload: function(response) {
                                try {
                                    const result = JSON.parse(response.responseText);
                                    resolve(result);
                                } catch (e) {
                                    reject(new Error('解析响应失败: ' + e.message));
                                }
                            },
                            onerror: function(error) {
                                reject(new Error('请求失败: ' + error.statusText));
                            }
                        });
                    } else {
                        fetch(`${this.config.apiBase}/api/emails/imap-fetch?sessionId=${sessionId}`, {
                            headers: headers
                        })
                        .then(response => response.json())
                        .then(resolve)
                        .catch(reject);
                    }
                });

                return data.success ? data.data : [];
            } catch (error) {
                logger.log('获取邮件失败: ' + error, 'error');
                throw error;
            }
        }

        /**
         * 从邮件内容中提取验证码
         */
        extractVerificationCode(emailContent) {
            const patterns = [
                /验证码[：:\s]*(\d{4,8})/i,
                /verification code[：:\s]*(\d{4,8})/i,
                /code[：:\s]*(\d{4,8})/i,
                /(\d{6})/,  // 6位数字
                /(\d{4})/   // 4位数字
            ];

            for (const pattern of patterns) {
                const match = emailContent.match(pattern);
                if (match && match[1]) {
                    return match[1];
                }
            }
            return null;
        }

        /**
         * 等待并获取验证码
         */
        async waitForVerificationCode(sessionId, maxRetries = 6, retryInterval = 5000) {
            for (let attempt = 0; attempt < maxRetries; attempt++) {
                logger.log(`尝试获取验证码 (第 ${attempt + 1}/${maxRetries} 次)...`);

                try {
                    const emails = await this.getEmails(sessionId);

                    if (emails && emails.length > 0) {
                        // 检查最新的邮件
                        const latestEmail = emails[0];
                        const emailContent = latestEmail.text || latestEmail.html || '';
                        const code = this.extractVerificationCode(emailContent);

                        if (code) {
                            logger.log('成功获取验证码: ' + code, 'success');
                            return code;
                        }
                    }

                    if (attempt < maxRetries - 1) {
                        logger.log(`未获取到验证码，${retryInterval/1000}秒后重试...`, 'warning');
                        await new Promise(resolve => setTimeout(resolve, retryInterval));
                    }
                } catch (error) {
                    logger.log('获取验证码出错: ' + error, 'error');
                    if (attempt < maxRetries - 1) {
                        await new Promise(resolve => setTimeout(resolve, retryInterval));
                    }
                }
            }

            throw new Error(`经过 ${maxRetries} 次尝试后仍未获取到验证码`);
        }
    }

    // 创建 XoxoMe API 实例
    const xoxomeAPI = new XoxoMeAPI({
        username: 'halo',
        password: '12345678aka'
    });

    // 存储当前邮箱信息
    let currentEmailInfo = null;

    // 添加CSS动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        @keyframes slideInUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes shimmer {
            0% { background-position: -200px 0; }
            100% { background-position: calc(200px + 100%) 0; }
        }

        #auto-register-log .log-entry {
            animation: slideInUp 0.3s ease-out;
        }

        #auto-register-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        #auto-register-btn:hover::before {
            left: 100%;
        }
    `;
    document.head.appendChild(style);

    // 颜色配置 - 更现代的配色方案
    const COLORS = {
        primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        primarySolid: '#667eea',
        secondary: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        secondarySolid: '#f093fb',
        success: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        successSolid: '#4facfe',
        danger: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        dangerSolid: '#fa709a',
        warning: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        warningSolid: '#fcb69f',
        info: '#6c7b7f',
        light: '#f8f9fa',
        dark: '#2d3748',
        background: 'linear-gradient(135deg, rgba(45, 55, 72, 0.98) 0%, rgba(68, 90, 120, 0.95) 100%)',
        cardBg: 'rgba(255, 255, 255, 0.08)',
        border: 'rgba(255, 255, 255, 0.12)',
        text: '#e2e8f0',
        textMuted: '#a0aec0'
    };

    // 日志UI配置 - 增强的配置选项
    const LOG_UI_CONFIG = {
        position: {
            bottom: 40,
            left: 20
        },
        dimensions: {
            width: 380,
            maxHeight: 500
        },
        animation: {
            duration: '0.4s',
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
        }
    };

    // 创建日志UI - 现代化设计风格
    function createLogUI() {
        const logContainer = document.createElement('div');
        logContainer.id = "auto-register-log";
        logContainer.style.cssText = `
            position: fixed;
            bottom: ${LOG_UI_CONFIG.position.bottom}px;
            left: ${LOG_UI_CONFIG.position.left}px;
            width: ${LOG_UI_CONFIG.dimensions.width}px;
            max-height: ${LOG_UI_CONFIG.dimensions.maxHeight}px;
            background: ${COLORS.background};
            border-radius: 16px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 8px 16px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid ${COLORS.border};
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            transform: translateY(0);
            transition: all ${LOG_UI_CONFIG.animation.duration} ${LOG_UI_CONFIG.animation.easing};
        `;

        logContainer.innerHTML = `
            <div style="
                padding: 18px 20px;
                background: ${COLORS.primary};
                color: white;
                font-weight: 700;
                font-size: 15px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: none;
                position: relative;
                overflow: hidden;
            ">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="
                        display: inline-block;
                        width: 8px;
                        height: 8px;
                        background: #00ff88;
                        border-radius: 50%;
                        box-shadow: 0 0 10px #00ff88;
                        animation: pulse 2s infinite;
                    "></span>
                    <span>🚀 注册填写助手</span>
                </div>
                <div style="display: flex; gap: 8px; align-items: center;">
                    <button id="auto-register-btn" style="
                        background: ${COLORS.secondary};
                        border: none;
                        color: white;
                        cursor: pointer;
                        font-size: 12px;
                        font-weight: 600;
                        padding: 8px 16px;
                        border-radius: 20px;
                        display: none;
                        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                        box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
                        position: relative;
                        overflow: hidden;
                    ">
                        <span style="position: relative; z-index: 1;">填写邮箱</span>
                    </button>
                    <button id="clear-log" style="
                        background: ${COLORS.cardBg};
                        border: 1px solid ${COLORS.border};
                        color: white;
                        cursor: pointer;
                        font-size: 12px;
                        font-weight: 500;
                        padding: 8px 12px;
                        border-radius: 8px;
                        transition: all 0.3s ease;
                        backdrop-filter: blur(10px);
                    ">🗑️</button>
                    <button id="minimize-log" style="
                        background: ${COLORS.cardBg};
                        border: 1px solid ${COLORS.border};
                        color: white;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 600;
                        padding: 8px 12px;
                        border-radius: 8px;
                        transition: all 0.3s ease;
                        backdrop-filter: blur(10px);
                    ">−</button>
                </div>
            </div>
            <div style="
                padding: 12px 20px;
                background: ${COLORS.cardBg};
                border-bottom: 1px solid ${COLORS.border};
                font-size: 13px;
                color: ${COLORS.textMuted};
                display: flex;
                align-items: center;
                justify-content: space-between;
                backdrop-filter: blur(10px);
            ">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="
                        font-size: 16px;
                        filter: drop-shadow(0 0 4px currentColor);
                    ">💻</span>
                    <span style="font-weight: 500;">操作控制台</span>
                </div>
                <div style="
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-size: 11px;
                    color: ${COLORS.textMuted};
                ">
                    <span style="
                        width: 6px;
                        height: 6px;
                        background: ${COLORS.successSolid};
                        border-radius: 50%;
                        display: inline-block;
                        animation: pulse 2s infinite;
                    "></span>
                    <span>在线</span>
                </div>
            </div>
            <div id="log-content" style="
                padding: 20px;
                overflow-y: auto;
                max-height: calc(${LOG_UI_CONFIG.dimensions.maxHeight}px - 160px);
                font-size: 13px;
                color: ${COLORS.text};
                line-height: 1.6;
                background: rgba(0, 0, 0, 0.1);
                scrollbar-width: thin;
                scrollbar-color: ${COLORS.border} transparent;
            "></div>
        `;

        document.body.appendChild(logContainer);

        // 最小化功
        let isMinimized = false;
        const logContent = document.getElementById('log-content');
        const minimizeBtn = document.getElementById('minimize-log');

        minimizeBtn.addEventListener('click', () => {
            isMinimized = !isMinimized;
            logContent.style.display = isMinimized ? 'none' : 'block';
            minimizeBtn.textContent = isMinimized ? '□' : '−';
            logContainer.style.transform = isMinimized ? 'scale(0.95)' : 'scale(1)';
        });

        // 清除日志功能
        const clearBtn = document.getElementById('clear-log');
        clearBtn.addEventListener('click', () => {
            logContent.innerHTML = '';
            log('日志已清除', 'info');
        });

        // 增强的按钮交互效果
        const registerBtn = document.getElementById('auto-register-btn');

        if (registerBtn) {
            registerBtn.addEventListener('mouseenter', () => {
                registerBtn.style.transform = 'scale(1.05) translateY(-1px)';
                registerBtn.style.boxShadow = '0 6px 20px rgba(240, 147, 251, 0.4)';
            });
            registerBtn.addEventListener('mouseleave', () => {
                registerBtn.style.transform = 'scale(1) translateY(0)';
                registerBtn.style.boxShadow = '0 4px 12px rgba(240, 147, 251, 0.3)';
            });
        }

        [clearBtn, minimizeBtn].forEach(btn => {
            if (btn) {
                btn.addEventListener('mouseenter', () => {
                    btn.style.transform = 'scale(1.1)';
                    btn.style.backgroundColor = 'rgba(255, 255, 255, 0.15)';
                });
                btn.addEventListener('mouseleave', () => {
                    btn.style.transform = 'scale(1)';
                    btn.style.backgroundColor = COLORS.cardBg;
                });
            }
        });

        return {
            log: function(message, type = 'info') {
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.style.marginBottom = '12px';
                logEntry.style.padding = '14px 16px';
                logEntry.style.borderRadius = '10px';
                logEntry.style.wordBreak = 'break-word';
                logEntry.style.transition = 'all 0.3s ease';
                logEntry.style.border = '1px solid transparent';
                logEntry.style.position = 'relative';
                logEntry.style.overflow = 'hidden';

                let bgColor, textColor, borderColor, icon;

                switch(type) {
                    case 'success':
                        bgColor = 'rgba(79, 172, 254, 0.15)';
                        textColor = COLORS.successSolid;
                        borderColor = 'rgba(79, 172, 254, 0.3)';
                        icon = '✅';
                        break;
                    case 'error':
                        bgColor = 'rgba(250, 112, 154, 0.15)';
                        textColor = COLORS.dangerSolid;
                        borderColor = 'rgba(250, 112, 154, 0.3)';
                        icon = '❌';
                        break;
                    case 'warning':
                        bgColor = 'rgba(252, 182, 159, 0.15)';
                        textColor = COLORS.warningSolid;
                        borderColor = 'rgba(252, 182, 159, 0.3)';
                        icon = '⚠️';
                        break;
                    default:
                        bgColor = COLORS.cardBg;
                        textColor = COLORS.text;
                        borderColor = COLORS.border;
                        icon = 'ℹ️';
                }

                logEntry.style.backgroundColor = bgColor;
                logEntry.style.color = textColor;
                logEntry.style.borderColor = borderColor;

                const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute:'2-digit', second:'2-digit' });
                logEntry.innerHTML = `
                    <div style="display: flex; align-items: flex-start; gap: 10px;">
                        <span style="font-size: 14px; margin-top: 1px;">${icon}</span>
                        <div style="flex: 1;">
                            <div style="font-size: 11px; color: ${COLORS.textMuted}; margin-bottom: 4px;">${time}</div>
                            <div style="font-weight: 500; line-height: 1.4;">${message}</div>
                        </div>
                    </div>
                `;

                logContent.appendChild(logEntry);
                logContent.scrollTop = logContent.scrollHeight;

                // 添加悬停效果
                logEntry.addEventListener('mouseenter', () => {
                    logEntry.style.transform = 'translateX(4px)';
                    logEntry.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                });
                logEntry.addEventListener('mouseleave', () => {
                    logEntry.style.transform = 'translateX(0)';
                    logEntry.style.boxShadow = 'none';
                });
            },
            showRegisterButton: function() {
                const registerBtn = document.getElementById('auto-register-btn');
                if (registerBtn) {
                    this.log('找到注册按钮，正在显示...');
                    registerBtn.style.display = 'inline-block';
                    return registerBtn;
                } else {
                    this.log('未找到注册按钮元素', 'error');
                    return null;
                }
            }
        };
    }

    // 创建全局日志对象
    const logger = createLogUI();

    // 生成随机邮箱（使用 XoxoMe API）
    async function generateEmail() {
        try {
            currentEmailInfo = await xoxomeAPI.createEmail();
            logger.log('成功创建邮箱: ' + currentEmailInfo.email, 'success');
            return currentEmailInfo.email;
        } catch (error) {
            logger.log('创建邮箱失败: ' + error, 'error');
            throw error;
        }
    }

    // 等待元素出现
    async function waitForElement(selector, timeout = 10000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) {
                return element;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        return null;
    }

    // 获取验证码（使用 XoxoMe API）
    async function getVerificationCode(maxRetries = 6, retryInterval = 5000) {
        if (!currentEmailInfo || !currentEmailInfo.sessionId) {
            throw new Error('没有可用的邮箱会话信息');
        }

        try {
            const code = await xoxomeAPI.waitForVerificationCode(
                currentEmailInfo.sessionId,
                maxRetries,
                retryInterval
            );
            return code;
        } catch (error) {
            logger.log("获取验证码失败: " + error, 'error');
            throw error;
        }
    }

    // 自动填写邮箱（不点击按钮）
    async function fillEmail() {
        try {
            const email = await generateEmail();
            logger.log('使用邮箱: ' + email);

            const emailInput = await waitForElement('input[name="username"]');
            if (!emailInput) {
                logger.log('未找到邮箱输入框', 'error');
                return false;
            }

            logger.log('找到邮箱输入框，开始填写');

            // 填写邮箱
            emailInput.value = email;
            emailInput.dispatchEvent(new Event('input', { bubbles: true }));
            emailInput.dispatchEvent(new Event('change', { bubbles: true }));

            logger.log('邮箱填写完成，请手动点击继续按钮', 'success');
            return true;
        } catch (error) {
            logger.log('填写邮箱失败: ' + error, 'error');
            return false;
        }
    }

    // 填写验证码（不点击按钮）
    async function fillVerificationCode() {
        const code = await getVerificationCode();
        if (!code) {
            logger.log('未能获取验证码', 'error');
            return false;
        }

        const codeInput = await waitForElement('input[name="code"]');
        if (!codeInput) {
            logger.log('未找到验证码输入框', 'error');
            return false;
        }

        // 填写验证码
        codeInput.value = code;
        codeInput.dispatchEvent(new Event('input', { bubbles: true }));
        codeInput.dispatchEvent(new Event('change', { bubbles: true }));

        logger.log('验证码填写完成，请手动点击继续按钮', 'success');
        return true;
    }

    // 同意服务条款（不点击注册按钮）
    async function completeRegistration() {
        const checkbox = await waitForElement('input[type="checkbox"]');
        if (checkbox) {
            if (!checkbox.checked) {
                checkbox.click();
                logger.log('已自动勾选服务条款同意框', 'success');
            }
        } else {
            logger.log('未找到服务条款复选框', 'warning');
        }

        logger.log('服务条款处理完成，请手动点击注册按钮', 'success');
        return true;
    }

    // 主函数
    async function main() {
        // 只在注册相关页面运行
        if (!window.location.href.includes('login.augmentcode.com') && !window.location.href.includes('auth.augmentcode.com')) {
            logger.log('当前页面不是注册页面，脚本不执行', 'info');
            return;
        }

        logger.log('===== 自动填写助手已启动 =====', 'info');
        logger.log('注意：脚本只负责填写信息，不会自动点击按钮', 'info');

        // 检查当前页面状态
        const emailInput = document.querySelector('input[name="username"]');
        const codeInput = document.querySelector('input[name="code"]');
        const termsCheckbox = document.querySelector('#terms-of-service-checkbox');

        if (emailInput) {
            logger.log('检测到邮箱输入页面');
            // 显示注册按钮
            const registerButton = logger.showRegisterButton();
            if (registerButton) {
                registerButton.addEventListener('click', async () => {
                    try {
                        registerButton.disabled = true;
                        registerButton.textContent = '处理中...';
                        registerButton.style.background = COLORS.warning;

                        if (await fillEmail()) {
                            logger.log('邮箱填写完成，请手动点击继续按钮', 'success');
                        }
                    } catch (error) {
                        logger.log('填写邮箱过程出错: ' + error, 'error');
                    } finally {
                        registerButton.disabled = false;
                        registerButton.textContent = '填写邮箱';
                        registerButton.style.background = COLORS.secondary;
                    }
                });
            }
        } else if (codeInput) {
            logger.log('检测到验证码输入页面，自动执行验证码填写...');
            try {
                if (await fillVerificationCode()) {
                    logger.log('验证码填写完成，请手动点击继续按钮', 'success');
                }
            } catch (error) {
                logger.log('填写验证码过程出错: ' + error, 'error');
            }
        } else if (termsCheckbox) {
            logger.log('检测到服务条款页面，自动勾选同意框...');
            try {
                if (!termsCheckbox.checked) {
                    termsCheckbox.click();
                    logger.log('已自动勾选服务条款同意框', 'success');
                }
                logger.log('服务条款处理完成，请手动点击注册按钮', 'success');
            } catch (error) {
                logger.log('勾选服务条款过程出错: ' + error, 'error');
            }
        } else {
            logger.log('无法识别当前页面状态', 'warning');
        }
    }

    // 启动脚本
    main().catch(error => logger.log('脚本执行出错: ' + error, 'error'));
})();