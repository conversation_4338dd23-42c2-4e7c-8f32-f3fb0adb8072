/**
 * XoxoMe API 测试脚本
 * 测试登录、创建邮箱、获取验证码功能
 */

// XoxoMe API 封装类（简化版，用于测试）
class XoxoMeAPITest {
    constructor(config = {}) {
        this.config = {
            apiBase: 'https://mail.xoxome.online',
            loginUrl: 'https://mail.xoxome.online/api/auth/login',
            username: config.username || 'halo',
            password: config.password || '12345678aka',
            ...config
        };

        this.token = null;
        this.user = null;
        this.availableSuffixes = null;
    }

    // 发送请求的通用方法
    async makeRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            if (typeof GM_xmlhttpRequest !== 'undefined') {
                // 使用Tampermonkey的GM_xmlhttpRequest
                GM_xmlhttpRequest({
                    method: options.method || 'GET',
                    url: url,
                    headers: options.headers || {},
                    data: options.body || null,
                    onload: function(response) {
                        try {
                            const result = JSON.parse(response.responseText);
                            resolve(result);
                        } catch (e) {
                            reject(new Error('解析响应失败: ' + e.message));
                        }
                    },
                    onerror: function(error) {
                        reject(new Error('请求失败: ' + error.statusText));
                    }
                });
            } else {
                // 使用普通fetch
                fetch(url, options)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(resolve)
                    .catch(reject);
            }
        });
    }

    // 登录
    async login() {
        console.log('🔐 开始登录...');
        try {
            const result = await this.makeRequest(this.config.loginUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: this.config.username,
                    password: this.config.password
                })
            });

            if (result.success) {
                this.token = result.token;
                this.user = result.user;
                console.log('✅ 登录成功:', result);
                return result;
            } else {
                throw new Error(result.message || '登录失败');
            }
        } catch (error) {
            console.error('❌ 登录失败:', error);
            throw error;
        }
    }

    // 获取请求头
    getHeaders() {
        const headers = {
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        };

        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
            headers['Cookie'] = `token=${this.token}`;
        }

        return headers;
    }

    // 获取可用域名后缀
    async getSuffixes() {
        console.log('📋 获取可用域名后缀...');
        if (!this.token) {
            await this.login();
        }

        try {
            const data = await this.makeRequest(`${this.config.apiBase}/api/email/suffixes`, {
                headers: this.getHeaders()
            });

            const suffixes = data.success ? data.data : [];
            this.availableSuffixes = suffixes;
            console.log('✅ 获取到域名后缀:', suffixes);
            return suffixes;
        } catch (error) {
            console.error('❌ 获取域名后缀失败:', error);
            throw error;
        }
    }

    // 创建邮箱
    async createEmail(suffix = null) {
        console.log('📧 创建临时邮箱...');
        if (!this.token) {
            await this.login();
        }

        // 如果没有指定后缀，获取并随机选择一个
        if (!suffix) {
            if (!this.availableSuffixes || this.availableSuffixes.length === 0) {
                await this.getSuffixes();
            }
            const randomIndex = Math.floor(Math.random() * this.availableSuffixes.length);
            suffix = this.availableSuffixes[randomIndex];
        }

        console.log('🎯 使用域名后缀:', suffix);

        try {
            const data = await this.makeRequest(`${this.config.apiBase}/api/email/generate`, {
                method: 'POST',
                headers: {
                    ...this.getHeaders(),
                    'Content-Type': 'application/json',
                    'Referer': 'https://mail.xoxome.online/dashboard',
                    'Origin': 'https://mail.xoxome.online'
                },
                body: JSON.stringify({
                    suffix: suffix
                })
            });

            if (data.success) {
                const sessionId = `sync_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
                const emailInfo = {
                    email: data.data.email,
                    prefix: data.data.prefix,
                    suffix: data.data.suffix,
                    sessionId: sessionId
                };
                console.log('✅ 邮箱创建成功:', emailInfo);
                return emailInfo;
            } else {
                throw new Error(data.message || '创建邮箱失败');
            }
        } catch (error) {
            console.error('❌ 创建邮箱失败:', error);
            throw error;
        }
    }

    // 获取邮件列表
    async getEmails(sessionId) {
        if (!this.token) {
            await this.login();
        }

        try {
            const data = await this.makeRequest(
                `${this.config.apiBase}/api/emails/imap-fetch?sessionId=${sessionId}`,
                {
                    headers: {
                        ...this.getHeaders(),
                        'Referer': 'https://mail.xoxome.online/dashboard'
                    }
                }
            );

            return data.success ? data.data : [];
        } catch (error) {
            console.error('❌ 获取邮件失败:', error);
            throw error;
        }
    }

    // 提取验证码
    extractVerificationCode(emailContent) {
        const patterns = [
            /验证码[：:\s]*(\d{4,8})/i,
            /verification code[：:\s]*(\d{4,8})/i,
            /code[：:\s]*(\d{4,8})/i,
            /(\d{6})/,
            /(\d{4})/
        ];

        for (const pattern of patterns) {
            const match = emailContent.match(pattern);
            if (match && match[1]) {
                return match[1];
            }
        }
        return null;
    }

    // 等待验证码
    async waitForVerificationCode(sessionId, maxRetries = 6, retryInterval = 5000) {
        console.log('⏳ 开始等待验证码...');
        
        for (let attempt = 0; attempt < maxRetries; attempt++) {
            console.log(`🔄 尝试获取验证码 (第 ${attempt + 1}/${maxRetries} 次)...`);

            try {
                const emails = await this.getEmails(sessionId);

                if (emails && emails.length > 0) {
                    console.log(`📬 获取到 ${emails.length} 封邮件`);
                    const latestEmail = emails[0];
                    const emailContent = latestEmail.text || latestEmail.html || '';
                    const code = this.extractVerificationCode(emailContent);

                    if (code) {
                        console.log('✅ 成功获取验证码:', code);
                        return code;
                    } else {
                        console.log('📧 邮件内容:', emailContent.substring(0, 200) + '...');
                    }
                } else {
                    console.log('📭 暂无邮件');
                }

                if (attempt < maxRetries - 1) {
                    console.log(`⏰ ${retryInterval/1000}秒后重试...`);
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            } catch (error) {
                console.error('❌ 获取验证码出错:', error);
                if (attempt < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            }
        }

        throw new Error(`经过 ${maxRetries} 次尝试后仍未获取到验证码`);
    }
}

// 测试函数
async function testXoxoMeAPI() {
    console.log('🚀 开始测试 XoxoMe API...');
    console.log('='.repeat(50));

    const api = new XoxoMeAPITest({
        username: 'halo',
        password: '12345678aka'
    });

    try {
        // 1. 测试登录
        console.log('\n1️⃣ 测试登录功能');
        await api.login();

        // 2. 测试获取域名后缀
        console.log('\n2️⃣ 测试获取域名后缀');
        const suffixes = await api.getSuffixes();

        // 3. 测试创建邮箱
        console.log('\n3️⃣ 测试创建邮箱');
        const emailInfo = await api.createEmail();

        // 4. 测试获取验证码（模拟等待）
        console.log('\n4️⃣ 测试获取验证码');
        console.log('💡 提示：现在可以向邮箱发送测试邮件来测试验证码获取功能');
        console.log('📧 测试邮箱:', emailInfo.email);
        console.log('🔑 会话ID:', emailInfo.sessionId);

        // 可选：等待验证码（如果有邮件发送到邮箱）
        // const code = await api.waitForVerificationCode(emailInfo.sessionId);

        console.log('\n✅ 所有测试完成！');
        console.log('='.repeat(50));

        return {
            success: true,
            emailInfo: emailInfo,
            api: api
        };

    } catch (error) {
        console.error('\n❌ 测试失败:', error);
        console.log('='.repeat(50));
        return {
            success: false,
            error: error.message
        };
    }
}

// 导出供使用
if (typeof window !== 'undefined') {
    window.XoxoMeAPITest = XoxoMeAPITest;
    window.testXoxoMeAPI = testXoxoMeAPI;
}

// 自动运行测试
console.log('📝 XoxoMe API 测试脚本已加载');
console.log('💻 运行测试: testXoxoMeAPI()');

// 如果在浏览器控制台中运行，可以取消注释下面这行
// testXoxoMeAPI();
