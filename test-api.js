/**
 * XoxoMe API 测试脚本
 */
class XoxoMeAPITest {
    constructor() {
        this.config = {
            apiBase: 'https://mail.xoxome.online',
            loginUrl: 'https://mail.xoxome.online/api/auth/login',
            username: 'halo',
            password: '12345678aka'
        };
        this.token = null;
        this.availableSuffixes = null;
    }

    async makeRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            if (typeof GM_xmlhttpRequest !== 'undefined') {
                GM_xmlhttpRequest({
                    method: options.method || 'GET',
                    url: url,
                    headers: options.headers || {},
                    data: options.body || null,
                    onload: function(response) {
                        try {
                            const result = JSON.parse(response.responseText);
                            resolve(result);
                        } catch (e) {
                            reject(new Error('解析响应失败: ' + e.message));
                        }
                    },
                    onerror: function(error) {
                        reject(new Error('请求失败: ' + error.statusText));
                    }
                });
            } else {
                // 检查是否有fetch函数
                if (typeof fetch === 'undefined') {
                    reject(new Error('当前环境不支持fetch，请在浏览器中运行或安装node-fetch'));
                    return;
                }
                fetch(url, options)
                    .then(response => response.json())
                    .then(resolve)
                    .catch(reject);
            }
        });
    }

    async login() {
        console.log('🔐 开始登录...');
        const result = await this.makeRequest(this.config.loginUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                username: this.config.username,
                password: this.config.password
            })
        });

        if (result.success) {
            this.token = result.token;
            console.log('✅ 登录成功');
            return result;
        } else {
            throw new Error(result.message || '登录失败');
        }
    }

    getHeaders() {
        return {
            'Accept': 'application/json',
            'Authorization': `Bearer ${this.token}`,
            'Cookie': `token=${this.token}`,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        };
    }

    async getSuffixes() {
        console.log('📋 获取域名后缀...');
        const data = await this.makeRequest(`${this.config.apiBase}/api/email/suffixes`, {
            headers: this.getHeaders()
        });
        
        this.availableSuffixes = data.success ? data.data : [];
        console.log('✅ 可用后缀:', this.availableSuffixes);
        return this.availableSuffixes;
    }

    async createEmail() {
        console.log('📧 创建邮箱...');
        if (!this.availableSuffixes?.length) {
            await this.getSuffixes();
        }
        
        const suffix = this.availableSuffixes[Math.floor(Math.random() * this.availableSuffixes.length)];
        console.log('🎯 使用后缀:', suffix);

        const data = await this.makeRequest(`${this.config.apiBase}/api/email/generate`, {
            method: 'POST',
            headers: {
                ...this.getHeaders(),
                'Content-Type': 'application/json',
                'Referer': 'https://mail.xoxome.online/dashboard',
                'Origin': 'https://mail.xoxome.online'
            },
            body: JSON.stringify({ suffix })
        });

        if (data.success) {
            const emailInfo = {
                email: data.data.email,
                sessionId: `sync_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
            };
            console.log('✅ 邮箱创建成功:', emailInfo.email);
            return emailInfo;
        } else {
            throw new Error(data.message || '创建邮箱失败');
        }
    }

    async getEmails(sessionId) {
        const data = await this.makeRequest(
            `${this.config.apiBase}/api/emails/imap-fetch?sessionId=${sessionId}`,
            { headers: { ...this.getHeaders(), 'Referer': 'https://mail.xoxome.online/dashboard' } }
        );
        return data.success ? data.data : [];
    }

    extractCode(content) {
        const patterns = [/验证码[：:\s]*(\d{4,8})/i, /code[：:\s]*(\d{4,8})/i, /(\d{6})/, /(\d{4})/];
        for (const pattern of patterns) {
            const match = content.match(pattern);
            if (match?.[1]) return match[1];
        }
        return null;
    }

    async waitForCode(sessionId, maxRetries = 6) {
        console.log('⏳ 等待验证码...');
        for (let i = 0; i < maxRetries; i++) {
            console.log(`🔄 第 ${i + 1}/${maxRetries} 次尝试`);

            const emails = await this.getEmails(sessionId);
            if (emails?.length > 0) {
                console.log(`📬 获取到 ${emails.length} 封邮件`);
                const latestEmail = emails[0];
                const content = latestEmail.text || latestEmail.html || '';

                // 显示邮件详细信息
                console.log('📧 最新邮件信息:');
                console.log('   发件人:', latestEmail.from || '未知');
                console.log('   主题:', latestEmail.subject || '无主题');
                console.log('   时间:', latestEmail.date || '未知');
                console.log('   内容预览:', content.substring(0, 200) + (content.length > 200 ? '...' : ''));

                const code = this.extractCode(content);
                if (code) {
                    console.log('✅ 获取到验证码:', code);
                    return code;
                } else {
                    console.log('❌ 未在邮件中找到验证码');
                }
            } else {
                console.log('📭 暂无邮件');
            }

            if (i < maxRetries - 1) {
                console.log('⏰ 5秒后重试...');
                await new Promise(r => setTimeout(r, 5000));
            }
        }
        throw new Error('未获取到验证码');
    }

    // 持续监控邮箱（无限循环）
    async startMonitoring(sessionId, interval = 5000) {
        console.log('🔄 开始持续监控邮箱...');
        console.log('📧 监控邮箱会话:', sessionId);
        console.log('⏰ 检查间隔:', interval / 1000, '秒');
        console.log('💡 按 Ctrl+C 停止监控');
        console.log('='.repeat(50));

        let lastEmailCount = 0;
        let monitorCount = 0;

        while (true) {
            try {
                monitorCount++;
                const timestamp = new Date().toLocaleTimeString();
                console.log(`\n[${timestamp}] 🔍 第 ${monitorCount} 次检查`);

                const emails = await this.getEmails(sessionId);

                if (emails?.length > 0) {
                    if (emails.length > lastEmailCount) {
                        console.log(`🎉 发现新邮件！总数: ${emails.length} (新增: ${emails.length - lastEmailCount})`);

                        // 显示所有新邮件
                        for (let i = 0; i < emails.length - lastEmailCount; i++) {
                            const email = emails[i];
                            const content = email.text || email.html || '';

                            console.log(`\n📧 邮件 ${i + 1}:`);
                            console.log('   发件人:', email.from || '未知');
                            console.log('   主题:', email.subject || '无主题');
                            console.log('   时间:', email.date || '未知');
                            console.log('   内容:', content.substring(0, 300) + (content.length > 300 ? '...' : ''));

                            const code = this.extractCode(content);
                            if (code) {
                                console.log('🎯 找到验证码:', code);
                                console.log('✅ 监控完成！');
                                return code;
                            }
                        }

                        lastEmailCount = emails.length;
                    } else {
                        console.log(`📬 邮箱中有 ${emails.length} 封邮件（无新邮件）`);
                    }
                } else {
                    console.log('📭 邮箱为空');
                }

                console.log(`⏰ ${interval / 1000}秒后继续检查...`);
                await new Promise(r => setTimeout(r, interval));

            } catch (error) {
                console.error('❌ 监控出错:', error.message);
                console.log('⏰ 5秒后重试...');
                await new Promise(r => setTimeout(r, 5000));
            }
        }
    }
}

// 发送测试邮件到指定邮箱（模拟验证码邮件）
async function sendTestEmail(email) {
    console.log('📤 模拟发送验证码邮件到:', email);
    // 这里只是模拟，实际中你需要真实发送邮件
    console.log('💡 请手动向邮箱发送包含验证码的测试邮件');
    console.log('💡 邮件内容示例: "您的验证码是：123456"');
}

// 完整测试函数（包含验证码获取）
async function testAPIComplete() {
    console.log('🚀 开始完整测试 XoxoMe API');
    console.log('='.repeat(50));

    const api = new XoxoMeAPITest();

    try {
        // 1. 登录
        console.log('\n1️⃣ 测试登录');
        await api.login();

        // 2. 创建邮箱
        console.log('\n2️⃣ 测试创建邮箱');
        const emailInfo = await api.createEmail();

        console.log('\n📧 测试邮箱:', emailInfo.email);
        console.log('🔑 会话ID:', emailInfo.sessionId);

        // 3. 模拟发送邮件
        console.log('\n3️⃣ 模拟发送验证码邮件');
        await sendTestEmail(emailInfo.email);

        // 4. 等待并获取验证码
        console.log('\n4️⃣ 等待验证码（30秒）');
        console.log('💡 请在30秒内向邮箱发送包含验证码的邮件');

        try {
            const code = await api.waitForCode(emailInfo.sessionId, 6);
            console.log('\n🎉 验证码获取成功:', code);
        } catch (error) {
            console.log('\n⚠️ 验证码获取超时:', error.message);
            console.log('💡 这是正常的，因为没有真实邮件发送');
        }

        // 保存到全局变量
        if (typeof window !== 'undefined') {
            window.testAPI = api;
            window.testEmailInfo = emailInfo;
        } else {
            global.testAPI = api;
            global.testEmailInfo = emailInfo;
        }

        console.log('\n✅ 完整测试完成！');
        console.log('='.repeat(50));

        return { api, emailInfo };

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.log('='.repeat(50));
        throw error;
    }
}

// 基础测试函数（只测试登录和创建邮箱）
async function testAPI() {
    console.log('🚀 开始基础测试 XoxoMe API');
    console.log('='.repeat(40));

    const api = new XoxoMeAPITest();

    try {
        // 登录
        await api.login();

        // 创建邮箱
        const emailInfo = await api.createEmail();

        console.log('\n📧 测试邮箱:', emailInfo.email);
        console.log('🔑 会话ID:', emailInfo.sessionId);
        console.log('\n💡 测试选项:');
        console.log('   1. 手动测试: testAPI.waitForCode("' + emailInfo.sessionId + '")');
        console.log('   2. 完整测试: testAPIComplete()');
        console.log('   3. 持续监控: startMonitoringTest()');
        console.log('   4. 监控当前邮箱: testAPI.startMonitoring("' + emailInfo.sessionId + '")');
        console.log('\n🔥 推荐使用监控模式，实时显示邮件内容！');

        // 返回API实例供进一步测试
        if (typeof window !== 'undefined') {
            window.testAPI = api;
            window.testEmailInfo = emailInfo;
        } else {
            global.testAPI = api;
            global.testEmailInfo = emailInfo;
        }

        console.log('\n✅ 基础测试完成！');
        console.log('='.repeat(40));

        return { api, emailInfo };

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.log('='.repeat(40));
        throw error;
    }
}

// 监控测试函数
async function startMonitoringTest() {
    console.log('🚀 开始邮箱监控测试');
    console.log('='.repeat(50));

    const api = new XoxoMeAPITest();

    try {
        // 登录
        await api.login();

        // 创建邮箱
        const emailInfo = await api.createEmail();

        console.log('\n📧 监控邮箱:', emailInfo.email);
        console.log('🔑 会话ID:', emailInfo.sessionId);
        console.log('\n💡 请向这个邮箱发送包含验证码的测试邮件');
        console.log('💡 系统将持续监控并显示所有邮件内容');
        console.log('💡 找到验证码后会自动停止');

        // 保存到全局变量
        if (typeof window !== 'undefined') {
            window.monitorAPI = api;
            window.monitorEmailInfo = emailInfo;
        } else {
            global.monitorAPI = api;
            global.monitorEmailInfo = emailInfo;
        }

        // 开始监控
        const code = await api.startMonitoring(emailInfo.sessionId, 3000); // 3秒检查一次
        console.log('\n🎉 最终获取到验证码:', code);

        return { api, emailInfo, code };

    } catch (error) {
        console.error('❌ 监控测试失败:', error.message);
        throw error;
    }
}

// 添加全局函数供调用
if (typeof window !== 'undefined') {
    window.testAPI = testAPI;
    window.testAPIComplete = testAPIComplete;
    window.startMonitoringTest = startMonitoringTest;
} else {
    global.testAPI = testAPI;
    global.testAPIComplete = testAPIComplete;
    global.startMonitoringTest = startMonitoringTest;
}

// 运行基础测试
testAPI();

